<!DOCTYPE html>
<html lang="uk">
<head>
  <meta charset="UTF-8">
  <title>Робот-Шукач Мін - Control Panel</title>
  <link rel="stylesheet" href="styles.css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="control-interface">
    <!-- Header -->
    <header class="control-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo-icon">
            <i class="fas fa-robot"></i>
          </div>
          <div class="logo-text">
            <h1>РОБОТ-ШУКАЧ МІН</h1>
            <span>Military Grade Detection System</span>
          </div>
        </div>
        <div class="status-indicators">
          <div class="status-item active">
            <div class="status-dot"></div>
            <span>ONLINE</span>
          </div>
          <div class="status-item">
            <i class="fas fa-signal"></i>
            <span>98%</span>
          </div>
          <div class="status-item">
            <i class="fas fa-battery-three-quarters"></i>
            <span>85%</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Control Area -->
    <div class="main-control">
      <!-- Left Panel - Controls -->
      <div class="control-panel">
        <div class="panel-section">
          <h3 class="section-title">
            <i class="fas fa-gamepad"></i>
            MOVEMENT CONTROL
          </h3>
          <div class="movement-controls">
            <div class="directional-pad">
              <button class="control-btn move-btn" id="move-forward" data-direction="up">
                <i class="fas fa-chevron-up"></i>
                <span>FORWARD</span>
              </button>
              <div class="middle-row">
                <button class="control-btn turn-btn" id="turn-left" data-direction="left">
                  <i class="fas fa-chevron-left"></i>
                  <span>LEFT</span>
                </button>
                <div class="center-indicator">
                  <div class="robot-indicator">
                    <i class="fas fa-robot"></i>
                  </div>
                </div>
                <button class="control-btn turn-btn" id="turn-right" data-direction="right">
                  <i class="fas fa-chevron-right"></i>
                  <span>RIGHT</span>
                </button>
              </div>
              <button class="control-btn move-btn" id="move-backward" data-direction="down">
                <i class="fas fa-chevron-down"></i>
                <span>BACKWARD</span>
              </button>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3 class="section-title">
            <i class="fas fa-search"></i>
            DETECTION SYSTEMS
          </h3>
          <div class="detection-controls">
            <button class="control-btn detection-btn mine-detect" id="detect-mine">
              <div class="btn-content">
                <i class="fas fa-exclamation-triangle"></i>
                <span>DETECT MINE</span>
                <div class="btn-glow mine-glow"></div>
              </div>
            </button>
            <button class="control-btn detection-btn obstacle-detect" id="detect-obstacle">
              <div class="btn-content">
                <i class="fas fa-cube"></i>
                <span>DETECT OBSTACLE</span>
                <div class="btn-glow obstacle-glow"></div>
              </div>
            </button>
          </div>
        </div>

        <div class="panel-section">
          <h3 class="section-title">
            <i class="fas fa-sliders-h"></i>
            SYSTEM PARAMETERS
          </h3>
          <div class="parameters">
            <div class="parameter-row">
              <label>Speed</label>
              <div class="slider-container">
                <input type="range" class="parameter-slider" min="0.05" max="0.3" step="0.05" value="0.1">
                <span class="slider-value">0.1 m/s</span>
              </div>
            </div>
            <div class="parameter-row">
              <label>Turn Rate</label>
              <div class="slider-container">
                <input type="range" class="parameter-slider" min="5" max="45" step="5" value="15">
                <span class="slider-value">204°/s</span>
              </div>
            </div>
            <div class="parameter-row">
              <label>Detection Range</label>
              <div class="slider-container">
                <input type="range" class="parameter-slider" min="0.2" max="1.0" step="0.1" value="0.5">
                <span class="slider-value">0.5 m</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Center Panel - Radar Display -->
      <div class="radar-panel">
        <div class="radar-container">
          <div class="radar-screen">
            <canvas id="canvas" width="500" height="500"></canvas>
            <div class="radar-overlay">
              <div class="radar-grid"></div>
              <div class="radar-rings">
                <div class="radar-ring" style="width: 100px; height: 100px;"></div>
                <div class="radar-ring" style="width: 200px; height: 200px;"></div>
                <div class="radar-ring" style="width: 300px; height: 300px;"></div>
                <div class="radar-ring" style="width: 400px; height: 400px;"></div>
              </div>
              <div class="radar-crosshair">
                <div class="crosshair-line horizontal"></div>
                <div class="crosshair-line vertical"></div>
              </div>
            </div>
          </div>
          <div class="radar-controls">
            <button class="radar-btn active">
              <i class="fas fa-crosshairs"></i>
              TRACKING
            </button>
            <button class="radar-btn">
              <i class="fas fa-expand"></i>
              ZOOM
            </button>
            <button class="radar-btn">
              <i class="fas fa-history"></i>
              HISTORY
            </button>
          </div>
        </div>
      </div>

      <!-- Right Panel - Data Display -->
      <div class="data-panel">
        <div class="panel-section">
          <h3 class="section-title">
            <i class="fas fa-tachometer-alt"></i>
            TELEMETRY
          </h3>
          <div class="telemetry-grid">
            <div class="telemetry-item">
              <div class="telemetry-label">Position</div>
              <div class="telemetry-value" id="robot-pos">(0.00, 0.00)</div>
              <div class="telemetry-unit">meters</div>
            </div>
            <div class="telemetry-item">
              <div class="telemetry-label">Heading</div>
              <div class="telemetry-value" id="robot-facing">0</div>
              <div class="telemetry-unit">degrees</div>
            </div>
            <div class="telemetry-item">
              <div class="telemetry-label">Distance</div>
              <div class="telemetry-value" id="distance-traveled">0.00</div>
              <div class="telemetry-unit">meters</div>
            </div>
            <div class="telemetry-item">
              <div class="telemetry-label">Speed</div>
              <div class="telemetry-value">0.00</div>
              <div class="telemetry-unit">m/s</div>
            </div>
            <div class="telemetry-item">
              <div class="telemetry-label">Detection Count</div>
              <div class="telemetry-value" id="detection-count">0/2</div>
              <div class="telemetry-unit">signals</div>
            </div>
            <div class="telemetry-item">
              <div class="telemetry-label">Auto Movement</div>
              <div class="telemetry-value" id="auto-movement-status">STOPPED</div>
              <div class="telemetry-unit">status</div>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3 class="section-title">
            <i class="fas fa-chart-line"></i>
            DETECTION LOG
          </h3>
          <div class="detection-log">
            <div class="log-header">
              <span>Time</span>
              <span>Type</span>
              <span>Coordinates</span>
            </div>
            <div class="log-entries" id="detection-log">
              <!-- Log entries will be added dynamically -->
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3 class="section-title">
            <i class="fas fa-keyboard"></i>
            KEYBOARD CONTROLS
          </h3>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-color" style="background: #007BFF; border-radius: 3px; width: 20px; height: 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">↑</div>
              <span>Auto Move Forward</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #007BFF; border-radius: 3px; width: 20px; height: 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">↓</div>
              <span>Auto Move Backward</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #007BFF; border-radius: 3px; width: 20px; height: 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">←</div>
              <span>Auto Turn Left</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #007BFF; border-radius: 3px; width: 20px; height: 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">→</div>
              <span>Auto Turn Right</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #ff4444; border-radius: 3px; width: 30px; height: 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 8px;">SPACE</div>
              <span>Detect Mine</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #888; border-radius: 3px; width: 20px; height: 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">O</div>
              <span>Detect Obstacle</span>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3 class="section-title">
            <i class="fas fa-info-circle"></i>
            LEGEND
          </h3>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-color mine-color"></div>
              <span>Mine Detected</span>
            </div>
            <div class="legend-item">
              <div class="legend-color obstacle-color"></div>
              <span>Obstacle</span>
            </div>
            <div class="legend-item">
              <div class="legend-color path-color"></div>
              <span>Robot Path</span>
            </div>
            <div class="legend-item">
              <div class="legend-color robot-color"></div>
              <span>Robot Position</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Status Bar -->
    <footer class="status-bar">
      <div class="status-section">
        <i class="fas fa-server"></i>
        <span>System: OPERATIONAL</span>
      </div>
      <div class="status-section">
        <i class="fas fa-clock"></i>
        <span id="current-time">00:00:00</span>
      </div>
      <div class="status-section">
        <i class="fas fa-map-marked"></i>
        <span>Scale: 1px = 0.02m</span>
      </div>
      <div class="status-section">
        <i class="fas fa-shield-alt"></i>
        <span>Security: ACTIVE</span>
      </div>
    </footer>
  </div>

  <script>
    const CANVAS_WIDTH = 500;
    const CANVAS_HEIGHT = 500;
    const METERS_PER_PIXEL = 0.02;
    const ROBOT_MOVE_DISTANCE = 0.1;
    const TURN_ANGLE = 15;
    const DETECTION_OFFSET = 0.5;

    let robotX = 0;
    let robotY = 0;
    let robotFacing = 0;
    let totalDistance = 0;
    let path = [{ x: 0, y: 0 }];
    let mines = [];
    let obstacles = [];

    // WebSocket connection
    let ws = null;

    // Metal detection counter
    let detectionCounter = 0;
    const DETECTION_THRESHOLD = 2;

    // Keyboard state tracking
    let keysPressed = {};
    let movementInterval = null;

    // Automatic movement state
    let isMovingForward = false;
    let isMovingBackward = false;
    let isTurningLeft = false;
    let isTurningRight = false;
    let autoMovementInterval = null;
    let autoTurnInterval = null;
    const AUTO_MOVEMENT_DELAY = 150; // 0.5 second
    const AUTO_TURN_DELAY = Math.round((TURN_ANGLE / 204) * 1000); // Calculate delay for 204°/sec

    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    function metersToPixels(meters) {
      return meters / METERS_PER_PIXEL;
    }

    function drawCanvas() {
      ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

      // Draw path with glow effect
      ctx.beginPath();
      path.forEach((point, index) => {
        const px = CANVAS_WIDTH / 2 + metersToPixels(point.x);
        const py = CANVAS_HEIGHT / 2 - metersToPixels(point.y);
        if (index === 0) ctx.moveTo(px, py);
        else ctx.lineTo(px, py);
      });
      ctx.strokeStyle = '#00aaff';
      ctx.lineWidth = 3;
      ctx.shadowColor = '#00aaff';
      ctx.shadowBlur = 10;
      ctx.stroke();
      ctx.shadowBlur = 0;

      // Draw mines with pulsing effect
      mines.forEach(mine => {
        const px = CANVAS_WIDTH / 2 + metersToPixels(mine.x);
        const py = CANVAS_HEIGHT / 2 - metersToPixels(mine.y);

        // Outer glow
        ctx.beginPath();
        ctx.arc(px, py, 8, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(255, 68, 68, 0.3)';
        ctx.fill();

        // Inner circle
        ctx.beginPath();
        ctx.arc(px, py, 4, 0, 2 * Math.PI);
        ctx.fillStyle = '#ff4444';
        ctx.fill();
      });

      // Draw obstacles
      obstacles.forEach(obstacle => {
        const px = CANVAS_WIDTH / 2 + metersToPixels(obstacle.x);
        const py = CANVAS_HEIGHT / 2 - metersToPixels(obstacle.y);

        ctx.beginPath();
        ctx.arc(px, py, 6, 0, 2 * Math.PI);
        ctx.fillStyle = '#888';
        ctx.fill();

        // Highlight
        ctx.beginPath();
        ctx.arc(px, py, 4, 0, 2 * Math.PI);
        ctx.fillStyle = '#aaa';
        ctx.fill();
      });

      // Draw robot with enhanced arrow
      const robotPx = CANVAS_WIDTH / 2 + metersToPixels(robotX);
      const robotPy = CANVAS_HEIGHT / 2 - metersToPixels(robotY);

      // Robot body
      ctx.beginPath();
      ctx.arc(robotPx, robotPy, 8, 0, 2 * Math.PI);
      ctx.fillStyle = '#007BFF';
      ctx.fill();

      // Direction arrow
      ctx.beginPath();
      ctx.moveTo(robotPx, robotPy);
      const arrowLength = 15;
      const arrowAngle = Math.PI / 6;
      const angle = (robotFacing * Math.PI) / 180 + Math.PI;
      ctx.lineTo(
        robotPx + arrowLength * Math.cos(angle - arrowAngle),
        robotPy - arrowLength * Math.sin(angle - arrowAngle)
      );
      ctx.moveTo(robotPx, robotPy);
      ctx.lineTo(
        robotPx + arrowLength * Math.cos(angle + arrowAngle),
        robotPy - arrowLength * Math.sin(angle + arrowAngle)
      );
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 3;
      ctx.stroke();
    }

    function updatePosition(distance) {
      const angleRad = (robotFacing * Math.PI) / 180;
      const dx = distance * Math.cos(angleRad);
      const dy = distance * Math.sin(angleRad);
      robotX += dx;
      robotY += dy;
      path.push({ x: robotX, y: robotY });
      totalDistance += Math.abs(distance);

      // Reset detection counter when robot moves
      detectionCounter = 0;
      updateDetectionCounter();

      drawCanvas();
      updateInfo();
    }

    function turn(angleDegrees) {
      robotFacing = (robotFacing + angleDegrees + 360) % 360;
      drawCanvas();
      updateInfo();
    }

    function detectObject(type) {
      if (type === 'mine') {
        // Only trigger metal detection on backend
        // The mine will be added via WebSocket response
        triggerMetalDetection();
      } else if (type === 'obstacle') {
        const angleRad = (robotFacing * Math.PI) / 180;
        const offsetX = DETECTION_OFFSET * Math.cos(angleRad);
        const offsetY = DETECTION_OFFSET * Math.sin(angleRad);
        const detectX = robotX + offsetX;
        const detectY = robotY + offsetY;

        obstacles.push({ x: detectX, y: detectY });
        // Add to detection log
        addToDetectionLog(type, detectX, detectY);
        drawCanvas();
      }
    }

    function addToDetectionLog(type, x, y) {
      const logEntry = document.createElement('div');
      logEntry.className = 'log-entry';
      const time = new Date().toLocaleTimeString();
      logEntry.innerHTML = `
        <span>${time}</span>
        <span class="${type}-type">${type.toUpperCase()}</span>
        <span>(${x.toFixed(2)}, ${y.toFixed(2)})</span>
      `;
      document.getElementById('detection-log').prepend(logEntry);
    }

    function updateInfo() {
      document.getElementById('robot-pos').textContent = `(${robotX.toFixed(2)}, ${robotY.toFixed(2)})`;
      document.getElementById('robot-facing').textContent = robotFacing.toFixed(0);
      document.getElementById('distance-traveled').textContent = totalDistance.toFixed(2);
    }

    function updateDetectionCounter() {
      document.getElementById('detection-count').textContent = `${detectionCounter}/${DETECTION_THRESHOLD}`;
    }

    function updateAutoMovementStatus() {
      const statusElement = document.getElementById('auto-movement-status');
      if (isMovingForward) {
        statusElement.textContent = 'FORWARD';
        statusElement.style.color = '#00ff00';
      } else if (isMovingBackward) {
        statusElement.textContent = 'BACKWARD';
        statusElement.style.color = '#ffaa00';
      } else if (isTurningLeft) {
        statusElement.textContent = 'TURN LEFT';
        statusElement.style.color = '#00aaff';
      } else if (isTurningRight) {
        statusElement.textContent = 'TURN RIGHT';
        statusElement.style.color = '#aa00ff';
      } else {
        statusElement.textContent = 'STOPPED';
        statusElement.style.color = '#ffffff';
      }
    }

    // Function to highlight button when key is pressed
    function highlightButton(buttonId) {
      const button = document.getElementById(buttonId);
      if (button) {
        button.style.transform = 'scale(0.95)';
        button.style.boxShadow = '0 0 20px rgba(0, 123, 255, 0.8)';
        setTimeout(() => {
          button.style.transform = '';
          button.style.boxShadow = '';
        }, 150);
      }
    }

    function updateTime() {
      document.getElementById('current-time').textContent = new Date().toLocaleTimeString();
    }

    // Function to send POST request
    function sendPostRequest(action) {
      fetch('http://193.169.189.125:8000/update/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: action }),
      })
      .then(response => response.json())
      .then(data => console.log(data))
      .catch((error) => {
        console.error('Error:', error);
      });
    }

    // Function to trigger metal detection on backend
    function triggerMetalDetection() {
      fetch('http://193.169.189.125:8000/drone_data/detect_metal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      })
      .then(response => response.json())
      .then(data => {
        console.log('Metal detection triggered:', data);
      })
      .catch((error) => {
        console.error('Error triggering metal detection:', error);
      });
    }

    // WebSocket functions
    function connectWebSocket() {
      ws = new WebSocket('ws://193.169.189.125:8000/drone_data/ws');

      ws.onopen = function(event) {
        console.log('WebSocket connected');
      };

      ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        console.log('WebSocket message received:', data);

        if (data.type === 'metal_detected') {
          detectionCounter++;
          console.log(`Detection count: ${detectionCounter}/${DETECTION_THRESHOLD}`);
          updateDetectionCounter();

          if (detectionCounter >= DETECTION_THRESHOLD) {
            // Add mine at current robot position with detection offset
            const angleRad = (robotFacing * Math.PI) / 180;
            const offsetX = DETECTION_OFFSET * Math.cos(angleRad);
            const offsetY = DETECTION_OFFSET * Math.sin(angleRad);
            const detectX = robotX + offsetX;
            const detectY = robotY + offsetY;

            mines.push({ x: detectX, y: detectY });
            addToDetectionLog('mine', detectX, detectY);
            drawCanvas();

            // Reset counter after successful detection
            detectionCounter = 0;
            updateDetectionCounter();
            console.log('Mine detected and added to map! Counter reset.');
          } else {
            console.log(`Detection signal received. Need ${DETECTION_THRESHOLD - detectionCounter} more detection(s).`);
          }
        }
      };

      ws.onclose = function(event) {
        console.log('WebSocket disconnected');
        // Try to reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
      };

      ws.onerror = function(error) {
        console.error('WebSocket error:', error);
      };
    }

    // Event listeners with POST requests
    document.getElementById('move-forward').addEventListener('click', () => {
      // First immediate movement
      sendPostRequest('moveForward');
      updatePosition(ROBOT_MOVE_DISTANCE);
      // Then start/stop automatic movement
      toggleAutoMovement('forward');
    });

    document.getElementById('move-backward').addEventListener('click', () => {
      // First immediate movement
      sendPostRequest('moveBackward');
      updatePosition(-ROBOT_MOVE_DISTANCE);
      // Then start/stop automatic movement
      toggleAutoMovement('backward');
    });

    document.getElementById('turn-left').addEventListener('click', () => {
      // First immediate turn
      sendPostRequest('rotateLeft');
      turn(TURN_ANGLE);
      // Then start/stop automatic turning
      toggleAutoTurn('left');
    });

    document.getElementById('turn-right').addEventListener('click', () => {
      // First immediate turn
      sendPostRequest('rotateRight');
      turn(-TURN_ANGLE);
      // Then start/stop automatic turning
      toggleAutoTurn('right');
    });

    document.getElementById('detect-mine').addEventListener('click', () => detectObject('mine'));
    document.getElementById('detect-obstacle').addEventListener('click', () => detectObject('obstacle'));

    // Keyboard controls
    document.addEventListener('keydown', function(event) {
      // Prevent default behavior for arrow keys to avoid page scrolling
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space'].includes(event.code)) {
        event.preventDefault();
      }

      // Avoid repeating actions if key is already pressed
      if (keysPressed[event.code]) {
        return;
      }
      keysPressed[event.code] = true;

      switch(event.code) {
        case 'ArrowUp':
          // Move forward
          highlightButton('move-forward');
          sendPostRequest('moveForward');
          updatePosition(ROBOT_MOVE_DISTANCE);
          toggleAutoMovement('forward');
          break;
        case 'ArrowDown':
          // Move backward
          highlightButton('move-backward');
          sendPostRequest('moveBackward');
          updatePosition(-ROBOT_MOVE_DISTANCE);
          toggleAutoMovement('backward');
          break;
        case 'ArrowLeft':
          // Turn left
          highlightButton('turn-left');
          sendPostRequest('rotateLeft');
          turn(TURN_ANGLE);
          toggleAutoTurn('left');
          break;
        case 'ArrowRight':
          // Turn right
          highlightButton('turn-right');
          sendPostRequest('rotateRight');
          turn(-TURN_ANGLE);
          toggleAutoTurn('right');
          break;
        case 'Space':
          // Detect mine
          highlightButton('detect-mine');
          detectObject('mine');
          break;
        case 'KeyO':
          // Detect obstacle
          highlightButton('detect-obstacle');
          detectObject('obstacle');
          break;
      }
    });

    document.addEventListener('keyup', function(event) {
      keysPressed[event.code] = false;
    });

    // Automatic movement functions
    function startAutoMovement(direction) {
      stopAllAutoActions(); // Stop any existing auto actions

      if (direction === 'forward') {
        isMovingForward = true;
        isMovingBackward = false;
      } else if (direction === 'backward') {
        isMovingForward = false;
        isMovingBackward = true;
      }

      updateAutoMovementStatus();

      autoMovementInterval = setInterval(() => {
        if (isMovingForward) {
          sendPostRequest('moveForward');
          updatePosition(ROBOT_MOVE_DISTANCE);
        } else if (isMovingBackward) {
          sendPostRequest('moveBackward');
          updatePosition(-ROBOT_MOVE_DISTANCE);
        }
      }, AUTO_MOVEMENT_DELAY);
    }

    function stopAutoMovement() {
      isMovingForward = false;
      isMovingBackward = false;
      if (autoMovementInterval) {
        clearInterval(autoMovementInterval);
        autoMovementInterval = null;
      }
      updateAutoMovementStatus();
    }

    function startAutoTurn(direction) {
      stopAllAutoActions(); // Stop any existing auto actions

      if (direction === 'left') {
        isTurningLeft = true;
        isTurningRight = false;
      } else if (direction === 'right') {
        isTurningLeft = false;
        isTurningRight = true;
      }

      updateAutoMovementStatus();

      autoTurnInterval = setInterval(() => {
        if (isTurningLeft) {
          sendPostRequest('rotateLeft');
          turn(TURN_ANGLE);
        } else if (isTurningRight) {
          sendPostRequest('rotateRight');
          turn(-TURN_ANGLE);
        }
      }, AUTO_TURN_DELAY);
    }

    function stopAutoTurn() {
      isTurningLeft = false;
      isTurningRight = false;
      if (autoTurnInterval) {
        clearInterval(autoTurnInterval);
        autoTurnInterval = null;
      }
      updateAutoMovementStatus();
    }

    function stopAllAutoActions() {
      stopAutoMovement();
      stopAutoTurn();
    }

    function toggleAutoMovement(direction) {
      if (direction === 'forward') {
        if (isMovingForward) {
          stopAllAutoActions();
        } else {
          startAutoMovement('forward');
        }
      } else if (direction === 'backward') {
        if (isMovingBackward) {
          stopAllAutoActions();
        } else {
          startAutoMovement('backward');
        }
      }
    }

    function toggleAutoTurn(direction) {
      if (direction === 'left') {
        if (isTurningLeft) {
          stopAllAutoActions();
        } else {
          startAutoTurn('left');
        }
      } else if (direction === 'right') {
        if (isTurningRight) {
          stopAllAutoActions();
        } else {
          startAutoTurn('right');
        }
      }
    }

    // Stop movement when window loses focus
    window.addEventListener('blur', function() {
      stopAllAutoActions();
      keysPressed = {};
    });

    // Initialize
    drawCanvas();
    updateInfo();
    updateDetectionCounter();
    updateAutoMovementStatus();
    updateTime();
    setInterval(updateTime, 1000);

    // Log turn rate calculation
    console.log(`Turn rate: 204°/s`);
    console.log(`Turn angle per step: ${TURN_ANGLE}°`);
    console.log(`Turn delay: ${AUTO_TURN_DELAY}ms`);
    console.log(`Actual turn rate: ${(TURN_ANGLE / AUTO_TURN_DELAY * 1000).toFixed(1)}°/s`);

    // Connect to WebSocket
    connectWebSocket();

    // Parameter slider updates
    document.querySelectorAll('.parameter-slider').forEach(slider => {
      slider.addEventListener('input', function() {
        const value = this.value;
        const unit = this.closest('.parameter-row').querySelector('.slider-value');
        if (this.min == '0.05') unit.textContent = `${value} m/s`;
        else if (this.min == '5') unit.textContent = `${value}°/s`;
        else unit.textContent = `${value} m`;
      });
    });
  </script>
</body>
</html>
