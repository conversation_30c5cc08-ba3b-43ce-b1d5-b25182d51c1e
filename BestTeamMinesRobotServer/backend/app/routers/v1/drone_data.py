from fastapi import Depends
from fastapi.routing import APIRouter

from app.repository import MetalDetectionRepository, get_metal_detection_repository
from app.schemas import MetalDetectionSchema, MetalDetectionCreateSchema

router = APIRouter(prefix="/drone_data")


@router.post("/detect_metal", response_model=MetalDetectionSchema)
async def detect_metal(
    detection_data: MetalDetectionCreateSchema,
    metal_detection_repository: MetalDetectionRepository = Depends(get_metal_detection_repository),
):
    print("METAL DETECTED!")
    detection = await metal_detection_repository.create_detection(
        x=detection_data.x,
        y=detection_data.y,
        robot_x=detection_data.robot_x,
        robot_y=detection_data.robot_y,
        robot_facing=detection_data.robot_facing,
    )
    return detection


@router.get("/detections", response_model=list[MetalDetectionSchema])
async def get_detections(
    metal_detection_repository: MetalDetectionRepository = Depends(get_metal_detection_repository),
):
    detections = await metal_detection_repository.get_all_detections()
    return [
        MetalDetectionSchema(
            x=d.x,
            y=d.y,
            robot_x=d.robot_x,
            robot_y=d.robot_y,
            robot_facing=d.robot_facing,
            timestamp=d.created_at.isoformat(),
        )
        for d in detections
    ]
